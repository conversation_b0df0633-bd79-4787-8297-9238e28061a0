import { InvestigationConfig } from '../types';
import {
  INVESTIGATION_DEFAULTS,
  RISK_THRESHOLDS,
  SUSPICIOUS_ACTIVITY_WEIGHTS,
  BITCOIN_ADDRESS_PATTERNS,
  TRANSACTION_ID_PATTERN,
  OUTPUT_FORMATS,
  EVIDENCE_TYPES,
  LOG_LEVELS,
} from '../utils/constants';

export const DEFAULT_CONFIG: InvestigationConfig = {
  apiBaseUrl: INVESTIGATION_DEFAULTS.API_BASE_URL,
  rateLimitDelay: INVESTIGATION_DEFAULTS.RATE_LIMIT_DELAY,
  maxRetries: INVESTIGATION_DEFAULTS.MAX_RETRIES,
  requestTimeout: INVESTIGATION_DEFAULTS.REQUEST_TIMEOUT,
  maxDepth: INVESTIGATION_DEFAULTS.MAX_DEPTH,
  saveReports: true,
  saveVisualizations: true,
  outputDirectory: INVESTIGATION_DEFAULTS.OUTPUT_DIRECTORY,
  enableAIInsights: true,
  verboseLogging: false,
};

// Re-export constants for backward compatibility
export {
  RISK_THRESHOLDS,
  SUSPICIOUS_ACTIVITY_WEIGHTS,
  BITCOIN_ADDRESS_PATTERNS,
  TRANSACTION_ID_PATTERN,
  OUTPUT_FORMATS,
  EVIDENCE_TYPES,
  LOG_LEVELS,
};
