import { TransactionInfo, PatternAnalysis, SuspiciousActivity, RiskAssessment } from '../types';
export declare class AnalysisService {
    private cache;
    private readonly CACHE_TTL;
    /**
     * Generate AI-powered insights based on transaction patterns
     */
    generateAIInsights(transactions: TransactionInfo[], patterns: PatternAnalysis, suspiciousActivity: SuspiciousActivity): string[];
    /**
     * Analyze transaction patterns for suspicious activity detection with caching
     */
    analyzeTransactionPatterns(transactions: TransactionInfo[]): PatternAnalysis;
    private analyzeTransactionPatternsSequential;
    private analyzeTransactionPatternsParallel;
    /**
     * Detect various types of suspicious activity patterns
     */
    detectSuspiciousActivity(transactions: TransactionInfo[]): SuspiciousActivity;
    /**
     * Generate comprehensive risk assessment
     */
    generateRiskAssessment(transactions: TransactionInfo[], patterns: PatternAnalysis, suspiciousActivity: SuspiciousActivity): RiskAssessment;
    private analyzeTimingPatterns;
    private analyzeAmountPatterns;
    private analyzeAddressReuse;
    private analyzeClusteringHints;
    private calculateRiskIndicators;
    private detectMixingServices;
    private detectExchangeDeposits;
    private detectPeelChains;
    private detectConsolidationPatterns;
    private detectPrivacyCoinInteractions;
    private classifySuspicionLevel;
    private generateRiskRecommendations;
    private getEmptyPatternAnalysis;
    /**
     * Performance optimization utility methods
     */
    private createCacheKey;
    private simpleHash;
    private chunkArray;
    private mergeTimingAnalysis;
    /**
     * Cache management methods
     */
    clearCache(): void;
    getCacheStats(): {
        patterns: number;
        suspicious: number;
        risk: number;
    };
}
//# sourceMappingURL=analysis.d.ts.map