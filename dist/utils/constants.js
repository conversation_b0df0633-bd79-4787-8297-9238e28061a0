"use strict";
/**
 * Shared constants for the Bitcoin Forensic Investigation Tool
 * Consolidates constants that were duplicated across multiple files
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOG_LEVELS = exports.EVIDENCE_TYPES = exports.OUTPUT_FORMATS = exports.ADDRESS_TYPE_COLORS = exports.RISK_COLORS = exports.EVIDENCE_CATEGORIES = exports.PERFORMANCE_CONSTANTS = exports.TIME_CONSTANTS = exports.FILE_SIZE_LIMITS = exports.QUALITY_THRESHOLDS = exports.INVESTIGATION_DEFAULTS = exports.SECURITY_DEFAULTS = exports.ERROR_ALERT_THRESHOLDS = exports.SUSPICIOUS_ACTIVITY_WEIGHTS = exports.RISK_THRESHOLDS = exports.TRANSACTION_ID_PATTERN = exports.BITCOIN_ADDRESS_PATTERNS = void 0;
// Bitcoin Address Patterns (consolidated from config/default.ts and validation.ts)
exports.BITCOIN_ADDRESS_PATTERNS = {
    LEGACY: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/,
    SEGWIT: /^bc1[a-z0-9]{39,59}$/,
    TAPROOT: /^bc1p[a-z0-9]{58}$/,
};
// Transaction ID Pattern
exports.TRANSACTION_ID_PATTERN = /^[a-fA-F0-9]{64}$/;
// Risk Assessment Thresholds (consolidated from multiple services)
exports.RISK_THRESHOLDS = {
    MINIMAL: 0,
    LOW: 2,
    MEDIUM: 5,
    HIGH: 8,
    CRITICAL: 12,
};
// Suspicious Activity Weights (consolidated from analysis services)
exports.SUSPICIOUS_ACTIVITY_WEIGHTS = {
    mixingServices: 3,
    exchangeDeposits: 2,
    peelChains: 2,
    consolidationPatterns: 1,
    privacyCoinInteractions: 3,
};
// Error Alert Thresholds (consolidated from error-handler.ts)
exports.ERROR_ALERT_THRESHOLDS = {
    API_REQUEST_FAILED: 5,
    SECURITY_VIOLATION: 1,
    DATA_INTEGRITY_ERROR: 1,
    MEMORY_LIMIT_EXCEEDED: 3,
    UNKNOWN_ERROR: 10,
};
// Security Configuration Defaults (consolidated from security.ts)
exports.SECURITY_DEFAULTS = {
    MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
    ALLOWED_FILE_EXTENSIONS: ['.json', '.txt', '.html', '.pdf', '.csv'],
    MAX_INPUT_LENGTH: 10000,
    RATE_LIMIT_WINDOW: 60 * 1000, // 1 minute
    MAX_REQUESTS_PER_WINDOW: 100,
};
// Investigation Configuration Defaults
exports.INVESTIGATION_DEFAULTS = {
    API_BASE_URL: 'https://blockstream.info/api/',
    RATE_LIMIT_DELAY: 100, // 100ms between requests
    MAX_RETRIES: 3,
    REQUEST_TIMEOUT: 30000, // 30 seconds
    MAX_DEPTH: 5,
    OUTPUT_DIRECTORY: 'investigation_results',
};
// Quality Assessment Thresholds
exports.QUALITY_THRESHOLDS = {
    MIN_COVERAGE: 80, // Minimum 80% test coverage
    MIN_PASS_RATE: 100, // 100% of critical tests must pass
    MAX_STARTUP_TIME: 3000, // CLI startup < 3 seconds
    MAX_BUILD_TIME: 60000, // Build time < 60 seconds
};
// File Size Limits
exports.FILE_SIZE_LIMITS = {
    LOG_FILE_MAX: 10 * 1024 * 1024, // 10MB
    ERROR_LOG_MAX: 5 * 1024 * 1024, // 5MB
    EVIDENCE_PACKAGE_MAX: 50 * 1024 * 1024, // 50MB
};
// Time Constants
exports.TIME_CONSTANTS = {
    ONE_HOUR_MS: 60 * 60 * 1000,
    ONE_DAY_MS: 24 * 60 * 60 * 1000,
    ONE_WEEK_MS: 7 * 24 * 60 * 60 * 1000,
    CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
};
// Performance Monitoring Constants
exports.PERFORMANCE_CONSTANTS = {
    SLOW_OPERATION_THRESHOLD: 10000, // 10 seconds
    MAX_METRICS_HISTORY: 1000,
    PERFORMANCE_SAMPLE_RATE: 0.1, // 10% sampling
};
// Evidence Categories (consolidated from evidence service)
exports.EVIDENCE_CATEGORIES = {
    TRANSACTION: 'transaction-evidence',
    ADDRESS: 'address-evidence',
    PATTERN: 'pattern-evidence',
    ANALYSIS: 'analysis-evidence',
    TECHNICAL: 'technical-evidence',
    SUMMARY: 'summary-evidence',
};
// Risk Level Colors (for visualization consistency)
exports.RISK_COLORS = {
    MINIMAL: '#28a745', // Green
    LOW: '#28a745', // Green
    MEDIUM: '#ffc107', // Yellow
    HIGH: '#fd7e14', // Orange
    CRITICAL: '#dc3545', // Red
};
// Address Type Colors (for visualization consistency)
exports.ADDRESS_TYPE_COLORS = {
    legacy: '#6c757d', // Gray
    segwit: '#007bff', // Blue
    taproot: '#6f42c1', // Purple
    unknown: '#dc3545', // Red
};
// Output Formats (consolidated from config/default.ts)
exports.OUTPUT_FORMATS = {
    JSON: 'json',
    PDF: 'pdf',
    HTML: 'html',
    CSV: 'csv',
    TXT: 'txt',
};
// Evidence Types (consolidated from config/default.ts)
exports.EVIDENCE_TYPES = {
    TRANSACTION: 'transaction',
    ADDRESS: 'address',
    PATTERN: 'pattern',
    ANALYSIS: 'analysis',
    SUMMARY: 'summary',
};
// Log Levels (consolidated from config/default.ts)
exports.LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
};
//# sourceMappingURL=constants.js.map