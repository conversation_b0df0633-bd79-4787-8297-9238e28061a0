/**
 * Shared constants for the Bitcoin Forensic Investigation Tool
 * Consolidates constants that were duplicated across multiple files
 */
export declare const BITCOIN_ADDRESS_PATTERNS: {
    readonly LEGACY: RegExp;
    readonly SEGWIT: RegExp;
    readonly TAPROOT: RegExp;
};
export declare const TRANSACTION_ID_PATTERN: RegExp;
export declare const RISK_THRESHOLDS: {
    readonly MINIMAL: 0;
    readonly LOW: 2;
    readonly MEDIUM: 5;
    readonly HIGH: 8;
    readonly CRITICAL: 12;
};
export declare const SUSPICIOUS_ACTIVITY_WEIGHTS: {
    readonly mixingServices: 3;
    readonly exchangeDeposits: 2;
    readonly peelChains: 2;
    readonly consolidationPatterns: 1;
    readonly privacyCoinInteractions: 3;
};
export declare const ERROR_ALERT_THRESHOLDS: {
    readonly API_REQUEST_FAILED: 5;
    readonly SECURITY_VIOLATION: 1;
    readonly DATA_INTEGRITY_ERROR: 1;
    readonly MEMORY_LIMIT_EXCEEDED: 3;
    readonly UNKNOWN_ERROR: 10;
};
export declare const SECURITY_DEFAULTS: {
    readonly MAX_FILE_SIZE: number;
    readonly ALLOWED_FILE_EXTENSIONS: readonly [".json", ".txt", ".html", ".pdf", ".csv"];
    readonly MAX_INPUT_LENGTH: 10000;
    readonly RATE_LIMIT_WINDOW: number;
    readonly MAX_REQUESTS_PER_WINDOW: 100;
};
export declare const INVESTIGATION_DEFAULTS: {
    readonly API_BASE_URL: "https://blockstream.info/api/";
    readonly RATE_LIMIT_DELAY: 100;
    readonly MAX_RETRIES: 3;
    readonly REQUEST_TIMEOUT: 30000;
    readonly MAX_DEPTH: 5;
    readonly OUTPUT_DIRECTORY: "investigation_results";
};
export declare const QUALITY_THRESHOLDS: {
    readonly MIN_COVERAGE: 80;
    readonly MIN_PASS_RATE: 100;
    readonly MAX_STARTUP_TIME: 3000;
    readonly MAX_BUILD_TIME: 60000;
};
export declare const FILE_SIZE_LIMITS: {
    readonly LOG_FILE_MAX: number;
    readonly ERROR_LOG_MAX: number;
    readonly EVIDENCE_PACKAGE_MAX: number;
};
export declare const TIME_CONSTANTS: {
    readonly ONE_HOUR_MS: number;
    readonly ONE_DAY_MS: number;
    readonly ONE_WEEK_MS: number;
    readonly CLEANUP_INTERVAL: number;
};
export declare const PERFORMANCE_CONSTANTS: {
    readonly SLOW_OPERATION_THRESHOLD: 10000;
    readonly MAX_METRICS_HISTORY: 1000;
    readonly PERFORMANCE_SAMPLE_RATE: 0.1;
};
export declare const EVIDENCE_CATEGORIES: {
    readonly TRANSACTION: "transaction-evidence";
    readonly ADDRESS: "address-evidence";
    readonly PATTERN: "pattern-evidence";
    readonly ANALYSIS: "analysis-evidence";
    readonly TECHNICAL: "technical-evidence";
    readonly SUMMARY: "summary-evidence";
};
export declare const RISK_COLORS: {
    readonly MINIMAL: "#28a745";
    readonly LOW: "#28a745";
    readonly MEDIUM: "#ffc107";
    readonly HIGH: "#fd7e14";
    readonly CRITICAL: "#dc3545";
};
export declare const ADDRESS_TYPE_COLORS: {
    readonly legacy: "#6c757d";
    readonly segwit: "#007bff";
    readonly taproot: "#6f42c1";
    readonly unknown: "#dc3545";
};
export declare const OUTPUT_FORMATS: {
    readonly JSON: "json";
    readonly PDF: "pdf";
    readonly HTML: "html";
    readonly CSV: "csv";
    readonly TXT: "txt";
};
export declare const EVIDENCE_TYPES: {
    readonly TRANSACTION: "transaction";
    readonly ADDRESS: "address";
    readonly PATTERN: "pattern";
    readonly ANALYSIS: "analysis";
    readonly SUMMARY: "summary";
};
export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly DEBUG: "debug";
};
//# sourceMappingURL=constants.d.ts.map