"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOG_LEVELS = exports.EVIDENCE_TYPES = exports.OUTPUT_FORMATS = exports.TRANSACTION_ID_PATTERN = exports.BITCOIN_ADDRESS_PATTERNS = exports.SUSPICIOUS_ACTIVITY_WEIGHTS = exports.RISK_THRESHOLDS = exports.DEFAULT_CONFIG = void 0;
const constants_1 = require("../utils/constants");
Object.defineProperty(exports, "RISK_THRESHOLDS", { enumerable: true, get: function () { return constants_1.RISK_THRESHOLDS; } });
Object.defineProperty(exports, "SUSPICIOUS_ACTIVITY_WEIGHTS", { enumerable: true, get: function () { return constants_1.SUSPICIOUS_ACTIVITY_WEIGHTS; } });
Object.defineProperty(exports, "BITCOIN_ADDRESS_PATTERNS", { enumerable: true, get: function () { return constants_1.BITCOIN_ADDRESS_PATTERNS; } });
Object.defineProperty(exports, "TRANSACTION_ID_PATTERN", { enumerable: true, get: function () { return constants_1.TRANSACTION_ID_PATTERN; } });
Object.defineProperty(exports, "OUTPUT_FORMATS", { enumerable: true, get: function () { return constants_1.OUTPUT_FORMATS; } });
Object.defineProperty(exports, "EVIDENCE_TYPES", { enumerable: true, get: function () { return constants_1.EVIDENCE_TYPES; } });
Object.defineProperty(exports, "LOG_LEVELS", { enumerable: true, get: function () { return constants_1.LOG_LEVELS; } });
exports.DEFAULT_CONFIG = {
    apiBaseUrl: constants_1.INVESTIGATION_DEFAULTS.API_BASE_URL,
    rateLimitDelay: constants_1.INVESTIGATION_DEFAULTS.RATE_LIMIT_DELAY,
    maxRetries: constants_1.INVESTIGATION_DEFAULTS.MAX_RETRIES,
    requestTimeout: constants_1.INVESTIGATION_DEFAULTS.REQUEST_TIMEOUT,
    maxDepth: constants_1.INVESTIGATION_DEFAULTS.MAX_DEPTH,
    saveReports: true,
    saveVisualizations: true,
    outputDirectory: constants_1.INVESTIGATION_DEFAULTS.OUTPUT_DIRECTORY,
    enableAIInsights: true,
    verboseLogging: false,
};
//# sourceMappingURL=default.js.map