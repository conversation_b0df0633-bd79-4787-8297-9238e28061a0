import { InvestigationConfig } from '../types';
import { RISK_THRESHOLDS, SUSPICIOUS_ACTIVITY_WEIGHTS, BITCOIN_ADDRESS_PATTERNS, TRANSACTION_ID_PATTERN, OUTPUT_FORMATS, EVIDENCE_TYPES, LOG_LEVELS } from '../utils/constants';
export declare const DEFAULT_CONFIG: InvestigationConfig;
export { RISK_THRESHOLDS, SUSPICIOUS_ACTIVITY_WEIGHTS, BITCOIN_ADDRESS_PATTERNS, TRANSACTION_ID_PATTERN, OUTPUT_FORMATS, EVIDENCE_TYPES, LOG_LEVELS, };
//# sourceMappingURL=default.d.ts.map